import { FC, ReactNode, useState, useRef } from 'react';

import { cva } from 'class-variance-authority';
import clsx from 'clsx';

import { cn } from '@/utils';

import { ToastVariant } from './Toast.types';
import { useSwipeGesture } from './useSwipeGesture';

import { Button, ButtonSize, ButtonVariant, IconButton } from '../Button';
import { Icon, IconName } from '../Icon';

// Global interaction counter to coordinate pause/resume across all toasts
let globalInteractionCount = 0;

const getIconName = (variant: ToastVariant): IconName => {
  switch (variant) {
    case ToastVariant.Info:
      return 'Bell';
    case ToastVariant.Success:
      return 'Check_ring';
    case ToastVariant.Warning:
      return 'Warning_ring';
    case ToastVariant.Error:
      return 'Error_ring';
    default:
      return 'Bell';
  }
};

const toastVariants = cva(
  'flex flex-auto border border-solid rounded-xl gap-8 w-full md:w-[480px] p-[16px]',
  {
    variants: {
      variant: {
        [ToastVariant.Info]:
          'bg-informer-info-fill border-informer-info-stroke',
        [ToastVariant.Success]:
          'bg-informer-success-fill border-informer-success-stroke',
        [ToastVariant.Warning]:
          'bg-informer-warning-fill border-informer-warning-stroke',
        [ToastVariant.Error]:
          'bg-informer-error-fill border-informer-error-stroke'
      }
    },
    defaultVariants: {
      variant: ToastVariant.Info
    }
  }
);

const toastIconVariants = cva('h-20 min-h-20 max-h-20 w-20 min-w-20 max-w-20', {
  variants: {
    variant: {
      [ToastVariant.Info]: 'text-informer-info-icon',
      [ToastVariant.Success]: 'text-informer-success-icon',
      [ToastVariant.Warning]: 'text-informer-warning-icon',
      [ToastVariant.Error]: 'text-informer-error-icon'
    }
  },
  defaultVariants: {
    variant: ToastVariant.Info
  }
});

export type ToastItemProps = {
  className?: string;
  toastId?: number;
  title?: ReactNode;
  titleClassName?: string;
  description?: ReactNode;
  descriptionClassName?: string;
  variant?: ToastVariant;
  showActions?: boolean;
  actionTitle?: string;
  onActionClick?: () => void;
  onCloseClick?: () => void;
  onPause?: () => void;
  onResume?: () => void;
};

export const ToastItem: FC<ToastItemProps> = ({
  className,
  toastId,
  variant = ToastVariant.Info,
  title,
  titleClassName,
  description,
  descriptionClassName,
  showActions,
  actionTitle = 'View',
  onActionClick,
  onCloseClick,
  onPause,
  onResume
}) => {
  const [isCloseVisible, setCloseVisible] = useState(false);
  const [isInteracting, setIsInteracting] = useState(false);
  const iconName = getIconName(variant);

  const swipeHandlers = useSwipeGesture(() => onCloseClick?.());

  const handleMouseEnter = () => {
    if (onCloseClick) {
      setCloseVisible(true);
    }
    if (onPause && !isInteracting) {
      setIsInteracting(true);
      onPause();
    }
  };

  const handleMouseLeave = () => {
    if (onCloseClick) {
      setCloseVisible(false);
    }
    if (onResume && isInteracting) {
      setIsInteracting(false);
      onResume();
    }
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    if (onPause && !isInteracting) {
      setIsInteracting(true);
      onPause();
    }
    swipeHandlers.onTouchStart(e);
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (onResume && isInteracting) {
      setIsInteracting(false);
      onResume();
    }
    swipeHandlers.onTouchEnd(e);
  };
  return (
    <div
      className={cn(
        'animate-fadeInUp toast-swipe-container relative z-[inherit] max-md:m-auto md:ml-auto',
        toastVariants({ variant, className })
      )}
      data-attributes="Toast"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onTouchStart={handleTouchStart}
      onTouchMove={swipeHandlers.onTouchMove}
      onTouchEnd={handleTouchEnd}
      style={swipeHandlers.style}
    >
      <Icon name={iconName} className={cn(toastIconVariants({ variant }))} />
      <div className="flex flex-auto flex-col gap-8 md:!flex-row">
        <div className="flex flex-auto flex-col justify-center gap-y-4">
          {title && (
            <p
              className={clsx(
                'text-body-1-medium text-text-heading',
                titleClassName
              )}
            >
              {title}
            </p>
          )}
          {description && (
            <p
              className={clsx(
                'line-clamp-2 text-body-1-regular text-text-body',
                descriptionClassName
              )}
            >
              {description}
            </p>
          )}
        </div>
        {showActions && (
          <div className="flex gap-16">
            <Button
              variant={ButtonVariant.Tertiary}
              size={ButtonSize.Small}
              onClick={onActionClick}
              className="h-[24px] min-w-0 bg-toast-button-fill-rested px-[12px] pb-[4px] pt-[2px]"
            >
              {actionTitle}
            </Button>
          </div>
        )}
      </div>

      <div className="absolute right-[-7px] top-[-8px]">
        <IconButton
          icon="Close_round_light"
          variant={ButtonVariant.Link}
          size={ButtonSize.ExtraSmall}
          className={clsx('rounded-full', { hidden: !isCloseVisible })}
          onClick={onCloseClick}
        />
      </div>
    </div>
  );
};
