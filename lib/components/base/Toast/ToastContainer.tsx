import { FC } from 'react';
import ReactDOM from 'react-dom';

import { DataAttributesProps } from '@/components/base/types';

import { ToastItem } from './ToastItem';
import { useToast } from './useToast';

export const ToastContainer: FC<DataAttributesProps> = ({ dataAttributes }) => {
  const { removeToast, toasts, pauseAllToasts, resumeAllToasts } = useToast();

  const renderToasts = () => {
    return (
      <>
        {toasts.map((toast) => (
          <ToastItem
            key={toast.id}
            toastId={toast.id}
            variant={toast.variant}
            title={toast.title}
            description={toast.description}
            onCloseClick={() => removeToast(toast.id)}
            showActions={toast.showActionButton}
            actionTitle={toast.actionTitle}
            onActionClick={toast.onActionClick}
            onPause={pauseAllToasts}
            onResume={resumeAllToasts}
          />
        ))}
      </>
    );
  };

  const portalRoot = Array.from(
    window.parent.document.querySelectorAll('#root > div')
  ).find((div) => {
    return (
      getComputedStyle(div).position === 'fixed' &&
      !!getComputedStyle(div).zIndex
    );
  });

  if (portalRoot) {
    return ReactDOM.createPortal(
      <div
        data-attributes={dataAttributes}
        className="pointer-events-auto relative z-[inherit] flex flex-col gap-12"
      >
        {renderToasts()}
      </div>,
      portalRoot
    );
  } else {
    return ReactDOM.createPortal(
      <div
        data-attributes={dataAttributes}
        className="fixed right-4 top-4 z-[1090] flex flex-col gap-12 bg-transparent pt-16 md:pr-16"
      >
        {renderToasts()}
      </div>,
      document.body
    );
  }
};
