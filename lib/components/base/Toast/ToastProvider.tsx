import {
  createContext,
  PropsWithChildren,
  useState,
  useRef,
  useCallback
} from 'react';

import { Toast, ToastContextType, ToastVariant } from './Toast.types';
import { ToastContainer } from './ToastContainer';

let toastId = 0;
export const ToastContext = createContext<ToastContextType | undefined>(
  undefined
);
const DEFAULT_TOAST_DELAY = 5000;

export const ToastProvider = ({ children }: PropsWithChildren) => {
  const [toasts, setToasts] = useState<Toast[]>([]);
  const activeTimersRef = useRef<Map<number, NodeJS.Timeout>>(new Map());
  const timerStartTimesRef = useRef<Map<number, number>>(new Map());
  const pausedRemainingTimesRef = useRef<Map<number, number>>(new Map());
  const globalPauseStateRef = useRef<boolean>(false);

  const startTimer = useCallback((id: number, duration: number = DEFAULT_TOAST_DELAY) => {
    // Don't start timer if globally paused
    if (globalPauseStateRef.current) {
      return;
    }

    const startTime = Date.now();
    const timeoutId = setTimeout(() => {
      setToasts((prev) => prev.filter((toast) => toast.id !== id));
      activeTimersRef.current.delete(id);
      timerStartTimesRef.current.delete(id);
    }, duration);

    activeTimersRef.current.set(id, timeoutId);
    timerStartTimesRef.current.set(id, startTime);
  }, []);

  const clearTimer = useCallback((id: number) => {
    const timeoutId = activeTimersRef.current.get(id);
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    // Clean up all tracking for this toast
    activeTimersRef.current.delete(id);
    timerStartTimesRef.current.delete(id);
    pausedRemainingTimesRef.current.delete(id);
  }, []);

  const pauseAllToasts = useCallback(() => {
    if (globalPauseStateRef.current) {
      return; // Already paused globally
    }

    globalPauseStateRef.current = true;
    const now = Date.now();

    // Calculate remaining time for each active timer before clearing
    activeTimersRef.current.forEach((timeoutId, toastId) => {
      const startTime = timerStartTimesRef.current.get(toastId);
      if (startTime) {
        const elapsed = now - startTime;
        const remainingTime = Math.max(0, DEFAULT_TOAST_DELAY - elapsed);
        pausedRemainingTimesRef.current.set(toastId, remainingTime);
      }
      clearTimeout(timeoutId);
    });

    // Clear active timers and start times
    activeTimersRef.current.clear();
    timerStartTimesRef.current.clear();
  }, []);

  const resumeAllToasts = useCallback(() => {
    if (!globalPauseStateRef.current) {
      return; // Not globally paused
    }

    globalPauseStateRef.current = false;

    // Start timers for all current toasts with their remaining time (or full delay for new toasts)
    toasts.forEach((toast) => {
      const remainingTime = pausedRemainingTimesRef.current.get(toast.id);
      if (remainingTime !== undefined) {
        // Resume with remaining time
        startTimer(toast.id, remainingTime);
      } else {
        // New toast added during pause, start with full delay
        startTimer(toast.id);
      }
    });

    // Clear the paused remaining times
    pausedRemainingTimesRef.current.clear();
  }, [toasts, startTimer]);

  const addToast = ({
    title,
    variant = ToastVariant.Info,
    description,
    actionTitle = 'View',
    onActionClick,
    showActionButton = false
  }: Omit<Toast, 'id'>) => {
    const id = toastId++;

    setToasts((prevToasts: Toast[]) => [
      ...prevToasts,
      {
        id,
        title,
        variant,
        description,
        showActionButton,
        actionTitle,
        onActionClick
      }
    ]);

    // Start the auto-dismiss timer (will respect global pause state)
    startTimer(id);
  };

  const pauseToast = useCallback((id: number) => {
    // Individual pause not needed in simplified implementation
    // This is kept for API compatibility but does nothing
    // Global pause/resume is handled by pauseAllToasts/resumeAllToasts
  }, []);

  const resumeToast = useCallback((id: number) => {
    // Individual resume not needed in simplified implementation
    // This is kept for API compatibility but does nothing
    // Global pause/resume is handled by pauseAllToasts/resumeAllToasts
  }, []);

  const removeToast = useCallback(
    (id: number) => {
      clearTimer(id);
      setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
    },
    [clearTimer]
  );

  return (
    <ToastContext.Provider
      value={{
        toasts,
        addToast,
        removeToast,
        pauseToast,
        resumeToast,
        pauseAllToasts,
        resumeAllToasts
      }}
    >
      <ToastContainer />
      {children}
    </ToastContext.Provider>
  );
};
