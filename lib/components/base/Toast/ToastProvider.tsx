import {
  createContext,
  PropsWithChildren,
  useState,
  useRef,
  useCallback
} from 'react';

import { Toast, ToastContextType, ToastVariant } from './Toast.types';
import { ToastContainer } from './ToastContainer';

let toastId = 0;
export const ToastContext = createContext<ToastContextType | undefined>(
  undefined
);
const DEFAULT_TOAST_DELAY = 5000;

interface ToastTimer {
  timeoutId: NodeJS.Timeout | null;
  remainingTime: number;
  startTime: number;
  isPaused: boolean;
}

export const ToastProvider = ({ children }: PropsWithChildren) => {
  const [toasts, setToasts] = useState<Toast[]>([]);
  const timersRef = useRef<Map<number, ToastTimer>>(new Map());
  const globalPauseStateRef = useRef<boolean>(false);

  const startTimer = useCallback(
    (id: number, duration: number = DEFAULT_TOAST_DELAY) => {
      const timer = timersRef.current.get(id);
      if (timer?.isPaused) {
        // Resume with remaining time
        const timeoutId = setTimeout(() => {
          setToasts((prev) => prev.filter((toast) => toast.id !== id));
          timersRef.current.delete(id);
        }, timer.remainingTime);

        timersRef.current.set(id, {
          ...timer,
          timeoutId,
          startTime: Date.now(),
          isPaused: false
        });
      } else {
        // Start new timer
        const timeoutId = setTimeout(() => {
          setToasts((prev) => prev.filter((toast) => toast.id !== id));
          timersRef.current.delete(id);
        }, duration);

        timersRef.current.set(id, {
          timeoutId,
          remainingTime: duration,
          startTime: Date.now(),
          isPaused: false
        });
      }
    },
    []
  );

  const clearTimer = useCallback((id: number) => {
    const timer = timersRef.current.get(id);
    if (timer?.timeoutId) {
      clearTimeout(timer.timeoutId);
    }
    timersRef.current.delete(id);
  }, []);

  const pauseToast = useCallback((id: number) => {
    const timer = timersRef.current.get(id);
    if (timer && !timer.isPaused && timer.timeoutId) {
      const elapsed = Date.now() - timer.startTime;
      const remainingTime = Math.max(0, timer.remainingTime - elapsed);

      clearTimeout(timer.timeoutId);
      timersRef.current.set(id, {
        ...timer,
        timeoutId: null,
        remainingTime,
        isPaused: true
      });
    }
  }, []);

  const resumeToast = useCallback(
    (id: number) => {
      const timer = timersRef.current.get(id);
      if (timer && timer.isPaused) {
        startTimer(id, timer.remainingTime);
      }
    },
    [startTimer]
  );

  const pauseAllToasts = useCallback(() => {
    if (globalPauseStateRef.current) {
      return; // Already paused globally
    }

    globalPauseStateRef.current = true;

    // Pause all active timers
    timersRef.current.forEach((timer, id) => {
      if (!timer.isPaused && timer.timeoutId) {
        const elapsed = Date.now() - timer.startTime;
        const remainingTime = Math.max(0, timer.remainingTime - elapsed);

        clearTimeout(timer.timeoutId);
        timersRef.current.set(id, {
          ...timer,
          timeoutId: null,
          remainingTime,
          isPaused: true
        });
      }
    });
  }, []);

  const resumeAllToasts = useCallback(() => {
    if (!globalPauseStateRef.current) {
      return; // Not globally paused
    }

    globalPauseStateRef.current = false;

    // Resume all paused timers
    timersRef.current.forEach((timer, id) => {
      if (timer.isPaused) {
        startTimer(id, timer.remainingTime);
      }
    });
  }, [startTimer]);

  const addToast = ({
    title,
    variant = ToastVariant.Info,
    description,
    actionTitle = 'View',
    onActionClick,
    showActionButton = false
  }: Omit<Toast, 'id'>) => {
    const id = toastId++;

    setToasts((prevToasts: Toast[]) => [
      ...prevToasts,
      {
        id,
        title,
        variant,
        description,
        showActionButton,
        actionTitle,
        onActionClick
      }
    ]);

    // Start the auto-dismiss timer
    startTimer(id);
  };

  const removeToast = useCallback(
    (id: number) => {
      clearTimer(id);
      setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
    },
    [clearTimer]
  );

  return (
    <ToastContext.Provider
      value={{
        toasts,
        addToast,
        removeToast,
        pauseToast,
        resumeToast,
        pauseAllToasts,
        resumeAllToasts
      }}
    >
      <ToastContainer />
      {children}
    </ToastContext.Provider>
  );
};
