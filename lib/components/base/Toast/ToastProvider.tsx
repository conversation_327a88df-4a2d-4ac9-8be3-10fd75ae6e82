import {
  createContext,
  PropsWithChildren,
  useState,
  useRef,
  useCallback
} from 'react';

import { Toast, ToastContextType, ToastVariant } from './Toast.types';
import { ToastContainer } from './ToastContainer';

let toastId = 0;
export const ToastContext = createContext<ToastContextType | undefined>(
  undefined
);
const DEFAULT_TOAST_DELAY = 5000;

export const ToastProvider = ({ children }: PropsWithChildren) => {
  const [toasts, setToasts] = useState<Toast[]>([]);
  const activeTimersRef = useRef<Map<number, NodeJS.Timeout>>(new Map());
  const globalPauseStateRef = useRef<boolean>(false);

  const startTimer = useCallback((id: number) => {
    // Don't start timer if globally paused
    if (globalPauseStateRef.current) {
      return;
    }

    const timeoutId = setTimeout(() => {
      setToasts((prev) => prev.filter((toast) => toast.id !== id));
      activeTimersRef.current.delete(id);
    }, DEFAULT_TOAST_DELAY);

    activeTimersRef.current.set(id, timeoutId);
  }, []);

  const clearTimer = useCallback((id: number) => {
    const timeoutId = activeTimersRef.current.get(id);
    if (timeoutId) {
      clearTimeout(timeoutId);
      activeTimersRef.current.delete(id);
    }
  }, []);

  const pauseAllToasts = useCallback(() => {
    if (globalPauseStateRef.current) {
      return; // Already paused globally
    }

    globalPauseStateRef.current = true;

    // Clear all active timers
    activeTimersRef.current.forEach((timeoutId) => {
      clearTimeout(timeoutId);
    });
    activeTimersRef.current.clear();
  }, []);

  const resumeAllToasts = useCallback(() => {
    if (!globalPauseStateRef.current) {
      return; // Not globally paused
    }

    globalPauseStateRef.current = false;

    // Start timers for all current toasts with full delay
    toasts.forEach((toast) => {
      startTimer(toast.id);
    });
  }, [toasts, startTimer]);

  const addToast = ({
    title,
    variant = ToastVariant.Info,
    description,
    actionTitle = 'View',
    onActionClick,
    showActionButton = false
  }: Omit<Toast, 'id'>) => {
    const id = toastId++;

    setToasts((prevToasts: Toast[]) => [
      ...prevToasts,
      {
        id,
        title,
        variant,
        description,
        showActionButton,
        actionTitle,
        onActionClick
      }
    ]);

    // Start the auto-dismiss timer (will respect global pause state)
    startTimer(id);
  };

  const removeToast = useCallback(
    (id: number) => {
      clearTimer(id);
      setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
    },
    [clearTimer]
  );

  return (
    <ToastContext.Provider
      value={{
        toasts,
        addToast,
        removeToast,
        pauseAllToasts,
        resumeAllToasts
      }}
    >
      <ToastContainer />
      {children}
    </ToastContext.Provider>
  );
};
