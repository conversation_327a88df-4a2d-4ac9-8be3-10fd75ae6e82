import { useCallback, useRef, useState } from 'react';

import { SwipeConfig } from '@/components';

interface SwipeGestureState {
  isDragging: boolean;
  translateX: number;
  opacity: number;
  isAnimating: boolean;
}

interface SwipeGestureHandlers {
  onTouchStart: (e: React.TouchEvent) => void;
  onTouchMove: (e: React.TouchEvent) => void;
  onTouchEnd: (e: React.TouchEvent) => void;
  style: React.CSSProperties;
}

export const SWIPE_CONFIG: Required<SwipeConfig> = {
  enabled: true,
  threshold: 100,
  velocityThreshold: 0.5,
  resistanceThreshold: 0.3,
  animationDuration: 300
};

export const useSwipeGesture = (
  onDismiss?: () => void
): SwipeGestureHandlers => {
  const [state, setState] = useState<SwipeGestureState>({
    isDragging: false,
    translateX: 0,
    opacity: 1,
    isAnimating: false
  });

  const gestureRef = useRef({
    startX: 0,
    startTime: 0,
    lastX: 0,
    lastTime: 0,
    isDragging: false
  });

  // Check if device supports touch
  const isTouchDevice = useCallback(() => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }, []);

  const calculateResistance = useCallback((distance: number): number => {
    const { threshold, resistanceThreshold } = SWIPE_CONFIG;
    if (Math.abs(distance) <= threshold) {
      return distance;
    }
    const excess = Math.abs(distance) - threshold;
    const resistedExcess = excess * resistanceThreshold;
    return distance > 0
      ? threshold + resistedExcess
      : -(threshold + resistedExcess);
  }, []);

  const calculateOpacity = useCallback((distance: number): number => {
    const absDistance = Math.abs(distance);
    const { threshold } = SWIPE_CONFIG;
    if (absDistance <= threshold) {
      return 1 - (absDistance / threshold) * 0.3; // Fade to 70% opacity at threshold
    }
    return 0.7; // Maintain 70% opacity beyond threshold
  }, []);

  const animateToPosition = useCallback(
    (targetX: number, targetOpacity: number, onComplete?: () => void) => {
      setState((prev) => ({ ...prev, isAnimating: true }));

      const startX = state.translateX;
      const startOpacity = state.opacity;
      const startTime = performance.now();
      const { animationDuration } = SWIPE_CONFIG;

      const animate = (currentTime: number) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / animationDuration, 1);

        // Easing function (ease-out)
        const easeOut = 1 - Math.pow(1 - progress, 3);

        const currentX = startX + (targetX - startX) * easeOut;
        const currentOpacity =
          startOpacity + (targetOpacity - startOpacity) * easeOut;

        setState((prev) => ({
          ...prev,
          translateX: currentX,
          opacity: currentOpacity
        }));

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          setState((prev) => ({ ...prev, isAnimating: false }));
          onComplete?.();
        }
      };

      requestAnimationFrame(animate);
    },
    [state.translateX, state.opacity]
  );

  const onTouchStart = useCallback(
    (e: React.TouchEvent) => {
      if (!isTouchDevice() || state.isAnimating || !onDismiss) {
        return;
      }

      const touch = e.touches[0];
      const now = performance.now();

      gestureRef.current = {
        startX: touch.clientX,
        startTime: now,
        lastX: touch.clientX,
        lastTime: now,
        isDragging: true
      };

      setState((prev) => ({ ...prev, isDragging: true }));
    },
    [isTouchDevice, state.isAnimating, onDismiss]
  );

  const onTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (!gestureRef.current.isDragging || state.isAnimating) {
        return;
      }

      e.preventDefault(); // Prevent scrolling while swiping

      const touch = e.touches[0];
      const now = performance.now();
      const deltaX = touch.clientX - gestureRef.current.startX;

      gestureRef.current.lastX = touch.clientX;
      gestureRef.current.lastTime = now;

      const resistedX = calculateResistance(deltaX);
      const opacity = calculateOpacity(resistedX);

      setState((prev) => ({
        ...prev,
        translateX: resistedX,
        opacity
      }));
    },
    [state.isAnimating, calculateResistance, calculateOpacity]
  );

  const onTouchEnd = useCallback(() => {
    if (!gestureRef.current.isDragging || state.isAnimating) {
      return;
    }

    const { startX, startTime, lastX, lastTime } = gestureRef.current;
    const { threshold, velocityThreshold } = SWIPE_CONFIG;

    const deltaX = lastX - startX;
    const deltaTime = lastTime - startTime;
    const velocity = Math.abs(deltaX) / deltaTime;

    gestureRef.current.isDragging = false;
    setState((prev) => ({ ...prev, isDragging: false }));

    // Check if swipe meets dismissal criteria
    const shouldDismiss =
      Math.abs(deltaX) >= threshold ||
      (Math.abs(deltaX) >= threshold * 0.5 && velocity >= velocityThreshold);

    if (shouldDismiss) {
      // Animate out and dismiss
      const direction = deltaX > 0 ? 1 : -1;
      const targetX = direction * window.innerWidth;
      animateToPosition(targetX, 0, () => onDismiss?.());
    } else {
      // Spring back to original position
      animateToPosition(0, 1);
    }
  }, [state.isAnimating, animateToPosition, onDismiss]);

  const style: React.CSSProperties = {
    transform: `translateX(${state.translateX}px)`,
    opacity: state.opacity,
    transition:
      state.isDragging || state.isAnimating
        ? 'none'
        : 'transform 0.2s ease, opacity 0.2s ease',
    touchAction: 'pan-y', // Allow vertical scrolling but handle horizontal gestures
    userSelect: 'none'
  };

  return {
    onTouchStart,
    onTouchMove,
    onTouchEnd,
    style
  };
};
