/* eslint-disable @typescript-eslint/no-explicit-any */

import { Meta, StoryObj } from '@storybook/react';

import { Toast, ToastVariant } from './Toast.types';
import { ToastProvider } from './ToastProvider';
import { useToast } from './useToast';

import { Button } from '../Button';

type Story = StoryObj;

const ComponenToastConsumer = ({
  variant,
  title,
  description,
  actionTitle,
  showActionButton,
  onActionClick
}: Omit<Toast, 'id'>) => {
  const { addToast } = useToast();

  return (
    <div>
      <Button
        onClick={() =>
          addToast({
            variant,
            title,
            description,
            actionTitle,
            showActionButton,
            onActionClick
          })
        }
      >
        Show Toast
      </Button>
    </div>
  );
};

const meta: Meta<typeof ComponenToastConsumer> = {
  title: 'Design System/Components/Toast/Toast',
  component: ComponenToastConsumer,
  tags: ['autodocs', 'In Review'],
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'The default toast message will stay on the screen for 5 second after which it will automatically disappear. Within this timeframe the user can choose to complete click on the button to preview the notification or ignore it.'
      }
    }
  },
  argTypes: {
    variant: {
      control: 'select',
      options: Object.values(ToastVariant),
      description: 'The visual variant of the toast message'
    },
    title: {
      control: 'text',
      description: 'The main heading text of the toast message'
    },
    description: {
      control: 'text',
      description: 'Optional descriptive text below the title'
    },
    actionTitle: {
      control: 'text',
      description: 'Text for the action button'
    },
    showActionButton: {
      control: 'boolean',
      description: 'Whether to show the action button'
    },
    onActionClick: {
      control: false,
      description: 'Callback function when action button is clicked'
    }
  },
  args: {
    variant: ToastVariant.Info,
    title: 'Heading text Lorem ipsum dolor sit amet!',
    description: '',
    actionTitle: 'View',
    showActionButton: false,
    onActionClick: () => alert('action clicked!')
  }
};

export default meta;

export const Default: Story = {
  render: (args: any) => (
    <ToastProvider>
      <ComponenToastConsumer {...args} />
    </ToastProvider>
  ),
  parameters: {
    docs: {
      source: {
        code: `
// Root Level:
<ToastProvider> // 👈 Add ToastProvider to the root of your applciation
  <ComponenToastConsumer {...args} />
</ToastProvider>

// Consumer: 
const ComponenToastConsumer = ({
  variant,
  title,
  description,
  actionTitle,
  showActionButton,
  onActionClick
}: Omit<Toast, 'id'>) => {
  const { addToast } = useToast(); 👈 Use Toast hook to show toast messages

  return (
    <div>
      <Button
        onClick={() =>
          addToast({
            variant,
            title,
            description,
            actionTitle,
            showActionButton,
            onActionClick
          })
        }
      >
        Show Toast
      </Button>
      <ToastContainer />
    </div>
  );
};
        `
      }
    }
  }
};

const VariantsTemplate = (args: any) => {
  const { addToast } = useToast();

  return (
    <div className="flex gap-20">
      <Button
        variant="Secondary"
        onClick={() =>
          addToast({
            ...args,
            variant: ToastVariant.Info,
            title: 'Info Toast Message'
          })
        }
      >
        Info Toast
      </Button>
      <Button
        variant="Secondary"
        onClick={() =>
          addToast({
            ...args,
            variant: ToastVariant.Success,
            title: 'Success Toast Message'
          })
        }
      >
        Success Toast
      </Button>
      <Button
        variant="Secondary"
        onClick={() =>
          addToast({
            ...args,
            variant: ToastVariant.Warning,
            title: 'Warning Toast Message'
          })
        }
      >
        Warning Toast
      </Button>
      <Button
        variant="Secondary"
        onClick={() =>
          addToast({
            ...args,
            variant: ToastVariant.Error,
            title: 'Error Toast Message'
          })
        }
      >
        Error Toast
      </Button>
    </div>
  );
};
export const InfoWithAction: Story = {
  name: 'Variants',
  render: (args) => (
    <ToastProvider>
      <VariantsTemplate {...args} />
    </ToastProvider>
  ),
  parameters: {
    docs: {
      source: {
        code: `
// Root Level:
<ToastProvider> // 👈 Add ToastProvider to the root of your applciation
  <ComponenToastConsumer {...args} />
</ToastProvider>

// Consumer: 
const ComponenToastConsumer = ({
  variant,
  title,
  description,
  actionTitle,
  showActionButton,
  onActionClick
}: Omit<Toast, 'id'>) => {
  const { addToast } = useToast(); 👈 Use Toast hook to show toast messages

  return (
    <div>
      <Button
        onClick={() =>
          addToast({
            variant,
            title,
            description,
            actionTitle,
            showActionButton,
            onActionClick
          })
        }
      >
        Show Toast
      </Button>
    </div>
  );
};
        `
      }
    }
  }
};

export const OnlyTitle: Story = {
  name: 'Title Only',
  render: (args: any) => (
    <ToastProvider>
      <ComponenToastConsumer {...args} />
    </ToastProvider>
  ),
  parameters: {
    docs: {
      source: {
        code: `
<Button
    onClick={() =>
      addToast({
        variant,
        title
      })
    }
  >
    Show Toast
  </Button>
</div>
        `
      }
    }
  }
};

export const TitleAndDescription: Story = {
  name: 'Title & Description',
  args: {
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.'
  },
  render: (args: any) => (
    <ToastProvider>
      <ComponenToastConsumer {...args} />
    </ToastProvider>
  ),
  parameters: {
    docs: {
      source: {
        code: `
<Button
    onClick={() =>
      addToast({
        variant,
        title,
        description
      })
    }
  >
    Show Toast
  </Button>
</div>
        `
      }
    }
  }
};

export const ActionButton: Story = {
  name: 'Action Button',
  args: {
    showActionButton: true
  },
  render: (args: any) => (
    <ToastProvider>
      <ComponenToastConsumer
        variant={args.variant}
        title={args.title}
        description={args.description}
        showActionButton={args.showActionButton}
        actionTitle={args.actionTitle}
        onActionClick={() => alert('Clicked!')}
      />
    </ToastProvider>
  ),
  parameters: {
    docs: {
      source: {
        code: `
<Button
    onClick={() =>
      addToast({
        variant,
        title,
        description,
        showActionButton
        actionTtitle,
        onActionClick: () => alert('Clicked!')
      })
    }
  >
    Show Toast
  </Button>
</div>
        `
      }
    }
  }
};

export const PauseResumeDemo: Story = {
  name: 'Pause/Resume Demo',
  args: {
    title: 'Hover or touch to pause auto-dismiss',
    description: 'This toast demonstrates the pause/resume functionality. On desktop, hover over the toast to pause the timer. On mobile, touch and hold to pause.',
    variant: ToastVariant.Info
  },
  render: (args: any) => (
    <ToastProvider>
      <div className="space-y-4">
        <ComponenToastConsumer {...args} />
        <div className="text-sm text-gray-600 max-w-md">
          <p><strong>Desktop:</strong> Hover over the toast to pause the auto-dismiss timer</p>
          <p><strong>Mobile:</strong> Touch and hold the toast to pause the timer</p>
          <p>The timer will resume when you stop interacting with the toast</p>
        </div>
      </div>
    </ToastProvider>
  ),
  parameters: {
    docs: {
      description: {
        story: 'This story demonstrates the pause/resume functionality. The auto-dismiss timer pauses when users interact with the toast and resumes when they stop.'
      }
    }
  }
};
