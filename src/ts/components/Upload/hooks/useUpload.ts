import { useCallback, useEffect, useState } from 'react';

import { useToast } from '@ot/onetalent-ui-kit';
import { isUndefined } from 'lodash';

import {
  DEFAULT_ACCEPTED_FILE_TYPES,
  DEFAULT_ALL_FILES_SIZE,
  DEFAULT_DISABLED,
  DEFAULT_FILE_LIST,
  DEFAULT_FILE_SIZE,
  DEFAULT_MAX_NUMBER_FILES
} from '../constants';
import {
  CustomFile,
  CustomFileStatuses,
  DefaultFileListInput,
  UploadEvents
} from '../types';
import {
  convertToBase64,
  formatFileSize,
  getFileExtension,
  mapFile2CustomFile
} from '../utils';

interface UseUploadProps
  extends Pick<
    UploadEvents,
    'beforeUpload' | 'onChange' | 'onRemove' | 'onRetryUpload'
  > {
  disabled?: boolean;
  maxCount?: number;
  acceptedFileTypes?: Array<string>;
  maxFileSize?: number;
  maxAllFilesSize?: number;

  defaultFileList?: DefaultFileListInput;
  fileList?: DefaultFileListInput;
}

export function useUpload({
  disabled = DEFAULT_DISABLED,
  maxCount = DEFAULT_MAX_NUMBER_FILES,
  acceptedFileTypes = DEFAULT_ACCEPTED_FILE_TYPES,
  maxFileSize = DEFAULT_FILE_SIZE,
  maxAllFilesSize = DEFAULT_ALL_FILES_SIZE,
  defaultFileList = DEFAULT_FILE_LIST,
  fileList,

  beforeUpload,
  onChange,
  onRemove,
  onRetryUpload
}: UseUploadProps) {
  const { addToast } = useToast();

  const isControlled = !isUndefined(fileList);

  const [internalFileList, setInternalFileList] =
    useState<DefaultFileListInput>(
      isControlled && !!fileList ? fileList : defaultFileList
    );

  useEffect(() => {
    if (isControlled && fileList) {
      setInternalFileList(fileList);
    }
  }, [fileList, isControlled]);

  const updateFileList = useCallback(
    async (
      actionFile: CustomFile,
      newList: DefaultFileListInput,
      event?: unknown
    ) => {
      onChange?.({
        file: actionFile,
        fileList: newList,
        event: event
      });

      if (!isControlled) {
        setInternalFileList(newList);
      }
    },
    [isControlled, onChange]
  );

  const handleChange = useCallback(
    async (fileListFromEvent: FileList | null, event?: unknown) => {
      if (disabled) {
        return;
      }

      if (!fileListFromEvent) return;

      const filesArray = Array.from(fileListFromEvent);

      if (internalFileList.length + filesArray.length > maxCount) {
        addToast({
          title: `You can only upload up to ${maxCount} files.`,
          variant: 'Error'
        });
        return;
      }

      const existingFileNames = internalFileList.map((file) => file.name);
      const hasDuplicate = filesArray.some((i) =>
        existingFileNames.includes(i.name)
      );
      if (hasDuplicate) {
        addToast({
          title: 'File Name needs to differ.',
          variant: 'Error'
        });
        return;
      }

      const hasInvalidType = filesArray.some((file) => {
        const ext = getFileExtension(file.name) ?? '';
        return !acceptedFileTypes.includes(`.${ext}`);
      });
      if (hasInvalidType) {
        addToast({
          title: 'Invalid file type.',
          variant: 'Error'
        });
        return;
      }

      const hasOversized = filesArray.some((file) => file.size > maxFileSize);
      if (hasOversized) {
        const fileSizeObj = formatFileSize(maxFileSize);
        addToast({
          title: `
        File size exceeds the maximum limit of ${
          fileSizeObj.total
        } ${fileSizeObj.size.toUpperCase()}.`,
          variant: 'Error'
        });
        return;
      }

      const hasTotalOversized =
        [...filesArray, ...internalFileList].reduce(
          (totalSize: number, file) => totalSize + (file.size ?? 0),
          0
        ) > maxAllFilesSize;

      if (hasTotalOversized) {
        const formattedAllFilesSize = formatFileSize(maxAllFilesSize);
        addToast({
          title: `Total files size exceeds the maximum limit of ${
            formattedAllFilesSize.total
          } ${formattedAllFilesSize.size.toUpperCase()}.`,
          variant: 'Error'
        });
        return;
      }

      const hasEmptyFile = filesArray.some((file) => file.size === 0);

      if (hasEmptyFile) {
        addToast({
          title: 'File cannot be empty.',
          variant: 'Error'
        });
        return;
      }

      const processedFiles: DefaultFileListInput = await Promise.all(
        filesArray.map(async (file) => {
          try {
            const base64 = await convertToBase64(file);
            return {
              ...mapFile2CustomFile(file),
              status: CustomFileStatuses.DONE,
              base64
            };
          } catch {
            addToast({
              title: `Failed to process file ${file.name}. Please try again.`,
              variant: 'Error'
            });
            return {
              ...mapFile2CustomFile(file),
              status: CustomFileStatuses.FAILURE,
              base64: ''
            };
          }
        })
      );

      const newFileList = [...internalFileList, ...processedFiles];
      const lastFile = processedFiles[processedFiles.length - 1];

      if (beforeUpload) {
        const result = await Promise.resolve(
          beforeUpload(lastFile, newFileList)
        );
        if (result === false) {
          return;
        }
      }

      await updateFileList(lastFile, newFileList, event);
    },
    [
      acceptedFileTypes,
      beforeUpload,
      maxAllFilesSize,
      disabled,
      internalFileList,
      maxCount,
      maxFileSize,
      addToast,
      updateFileList
    ]
  );

  const handleRemove = useCallback(
    async (file: CustomFile, event?: unknown) => {
      if (disabled) {
        return;
      }

      if (!internalFileList.some((f) => f.id === file.id)) return;

      if (onRemove) {
        const result = await Promise.resolve(onRemove(file));
        if (result === false) {
          return;
        }
      }

      const newList = internalFileList.filter((f) => f.id !== file.id);

      await updateFileList(file, newList, event);
    },
    [disabled, internalFileList, onRemove, updateFileList]
  );

  const handleRetryUpload = useCallback(
    async (file: CustomFile, event?: unknown) => {
      if (disabled) {
        return;
      }

      if (!internalFileList.some((f) => f.id === file.id)) return;

      if (onRetryUpload) {
        const result = await Promise.resolve(onRetryUpload(file));
        if (result === false) {
          return;
        }
      }

      const idx = internalFileList.findIndex((f) => f.id === file.id);
      if (idx < 0) return;

      const fileToRetry = internalFileList[idx];
      if (!fileToRetry.originFile) return;

      try {
        const base64 = await convertToBase64(fileToRetry.originFile);
        const newFile: CustomFile = {
          ...fileToRetry,
          base64,
          status: CustomFileStatuses.DONE
        };

        const newList = [
          ...internalFileList.slice(0, idx),
          newFile,
          ...internalFileList.slice(idx + 1)
        ];

        await updateFileList(newFile, newList, event);
      } catch {
        const failed: CustomFile = {
          ...fileToRetry,
          status: CustomFileStatuses.FAILURE
        };
        const newList = [
          ...internalFileList.slice(0, idx),
          failed,
          ...internalFileList.slice(idx + 1)
        ];

        await updateFileList(failed, newList, event);
      }
    },
    [disabled, internalFileList, onRetryUpload, updateFileList]
  );

  return {
    internalFileList,
    isControlled,
    handleChange,
    handleRemove,
    handleRetryUpload
  };
}
